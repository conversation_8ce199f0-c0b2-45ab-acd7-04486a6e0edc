import { B<PERSON>er<PERSON>ontext, Page } from 'playwright';
import { AccountInfo, AppUserData, AppUserDataResponse, OAuth } from '../types/index.js';
import * as crypto from 'crypto'
import { Config } from './config.service.js';
import { Logger } from './logger.service.js';
import { SearchService } from './search.service.js';
import { getRandomInRange, shuffleArray } from '../utils/random.js';
import { BrowserService } from './browser.service.js';
import { SessionService } from './session.service.js';
import moment from 'moment-timezone';

// 自定义授权码错误类
export class AuthorizationCodeError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'AuthorizationCodeError';
    }
}
//负责移动端任务的处理
export class MobileService {

    private static instance: MobileService;
    private logger = Logger.getInstance();
    private config = Config.getInstance().getConfig();
    public userData: AppUserDataResponse | undefined;
    public mobileSearchCount: number = 0; //本次搜索了的移动端次数
    private constructor() { }

    public static getInstance(): MobileService {
        if (!MobileService.instance) {
            MobileService.instance = new MobileService();
        }
        return MobileService.instance;
    }

    async do(mobileContext: BrowserContext, homePage: Page, account: AccountInfo, pcSearchCount: number): Promise<void> {
        if (account.onlyLogin) {
            return;
        }
        const accessToken = await this.getMobileAccessToken(homePage, account.email)
        this.userData = await this.getAppUserData(accessToken)

        this.logger.info(account.email, `当前点数:${this.userData.balance}`);
        const tasks = [];

        if (account.dailyTask) {
            tasks.push({
                name: 'CheckIn',
                execute: () => this.handleCheckIn(accessToken, this.userData!, account)
            });
            tasks.push({
                name: 'ReadToEarn',
                execute: () => this.handleReadToEarn(accessToken, this.userData!, account)
            });
        }
        else {
            tasks.push({
                name: 'Mobile Search',
                execute: () => this.handleSearch(mobileContext, this.userData!, account, pcSearchCount)
            });
            tasks.push({
                name: 'ReadToEarn',
                execute: () => this.handleReadToEarn(accessToken, this.userData!, account)
            });
            tasks.push({
                name: 'CheckIn',
                execute: () => this.handleCheckIn(accessToken, this.userData!, account)
            });
        }

        // 随机打乱任务顺序
        const shuffledTasks = shuffleArray(tasks);

        // 依次执行任务
        for (const task of shuffledTasks) {
            try {
                this.logger.info(account.email, `执行任务: ${task.name}`);
                await task.execute();
            } catch (error) {
                this.logger.error(account.email, `执行任务${task.name}时出错: ${error}`);
            }
            //注释掉等待,减少执行时间
            //await new Promise(resolve => setTimeout(resolve, getRandomInRange(3000, 5000)));
        }
        this.userData = await this.getAppUserData(accessToken)
        this.logger.info(account.email, `当前点数:${this.userData.balance}`);
    }


    public pcSearchPointProgress(userData: AppUserDataResponse): number {
        let pointProgress = 0;
        //WW_searchsrhptsexp2_global_level1_PC
        //WW_searchsrhptsexp2_global_level2_PC
        const searchs = userData.promotions.filter((x) => x.attributes.offerid && x.attributes.offerid.startsWith("WW_searchsrhptsexp2") && x.attributes.offerid.endsWith("PC"));
        if (searchs.length <= 0) {
            return pointProgress;
        }
        const search = searchs[0];
        return parseInt(search.attributes.progress);
    }

    public mobileSearchPointProgress(userData: AppUserDataResponse): number {
        let pointProgress = 0;
        //WW_searchsrhptsexp2_global_mobile
        const searchs = userData.promotions.filter((x) => x.attributes.offerid && x.attributes.offerid.startsWith("WW_searchsrhptsexp2") && x.attributes.offerid.endsWith("mobile"));
        if (searchs.length <= 0) {
            return pointProgress;
        }
        const search = searchs[0];
        return parseInt(search.attributes.progress);
    }

    async handleSearch(mobileContext: BrowserContext, userData: AppUserDataResponse, account: AccountInfo, pcSearchCount: number) {
        this.mobileSearchCount = 0; //重置移动端搜索次数
        const searchs = userData.promotions.filter((x) => x.attributes.offerid && x.attributes.offerid.startsWith("WW_searchsrhptsexp2") && x.attributes.offerid.endsWith("mobile"));
        if (searchs.length > 0) {
            const search = searchs[0];
            const searchEarnablePoints = (parseInt(search.attributes.max) - parseInt(search.attributes.progress));

            if (searchEarnablePoints > 0) {
                this.logger.info(account.email, `mobile搜索尚有${searchEarnablePoints}分可领取`);

                const page = await mobileContext.newPage();

                //本地剩余搜索次数
                let searchCount = searchEarnablePoints / 3;

                let limitCount = this.config.search.defaultMaxSearchPerRequest;
                const searchLimit = Config.getInstance().getLimitByScore(userData.balance);

                // 首先检查账号中是否有自定义的搜索限制
                if (account && account.maxSearchPerRequest != undefined && account.maxSearchPerRequest >= 0) {
                    // 如果账号中有自定义的搜索限制且大于0，则使用账号中的限制
                    limitCount = account.maxSearchPerRequest;
                } else if (searchLimit && searchLimit.maxSearchPerRequest != undefined && searchLimit.maxSearchPerRequest >= 0) {
                    // 如果账号中没有自定义限制，则使用分数对应的限制
                    limitCount = searchLimit.maxSearchPerRequest;
                }

                searchCount = Math.min(searchCount, limitCount - pcSearchCount);
                if (searchCount <= 0) {
                    return;
                }
                try {
                    const searchTotalCount = (this.pcSearchPointProgress(userData) + this.mobileSearchPointProgress(userData)) / 3;

                    this.mobileSearchCount = await SearchService.getInstance().doSearch(page, searchCount, userData.balance, searchTotalCount, account);
                }
                catch (error) {
                    const browserService = BrowserService.getInstance();
                    this.logger.error(account.email, `移动端设备${browserService.deviceName},搜索错误: ${error}`)
                }
                finally {
                    await page.close()
                }
            }
            else {
                this.logger.info(account.email, `search今天已完成`);
            }
        }
    }

    async handleCheckIn(accessToken: string, userData: AppUserDataResponse, account: AccountInfo) {
        //每日签到
        const checkins = userData.promotions.filter((x) => x.attributes.offerid === 'Gamification_Sapphire_DailyCheckIn');
        //console.log(JSON.stringify(userData.promotions))
        if (checkins.length > 0) {
            const checkin = checkins[0];

            // if (checkin.attributes.hidden.toLowerCase() === "true")
            // {
            //     return;
            // }

            // 检查是否今天已经签到过了
            const lastUpdated = moment(checkin.attributes.last_updated);
            const today = moment().tz('Asia/Shanghai');
            const isAlreadyCheckedInToday = lastUpdated.tz('Asia/Shanghai').isSame(today, 'day');

            //必应app签到
            if (!isAlreadyCheckedInToday) {
                const response = await fetch('https://prod.rewardsplatform.microsoft.com/dapi/me/activities', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json',
                        'X-Rewards-Country': this.config.reward.country,
                        'X-Rewards-Language': this.config.reward.language,
                        "x-rewards-appid": "SAAndroid/30.4.430116016",
                        "x-rewards-ismobile": "true"
                    },
                    body: JSON.stringify({
                        amount: 1,
                        country: this.config.reward.country,
                        id: crypto.randomBytes(64).toString('hex'),
                        type: 101,
                        attributes: {
                            offerid: 'Gamification_Sapphire_DailyCheckIn'
                        }
                    })
                });
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const responseData = await response.json();
                try {
                    const claimedPoint = parseInt(responseData.response.activity.p);
                    //p>0表示领取的积分
                    this.logger.info('DAILY-CHECK-IN', claimedPoint > 0 ? `签到成功,获取${claimedPoint}点` : '今日已签到')
                }
                catch (error) {
                    this.logger.error('DAILY-CHECK-IN ', `错误:${error},响应:${JSON.stringify(responseData)}`)
                }
            }
            else {
                this.logger.info(account.email, `今日已签到 (last_updated: ${lastUpdated.tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')})`);
            }
        }
    }

    /**
     * 获取随机搜索次数
     */
    private getRandomReadCount(maxSearchCount: number, range: number = 1) {
        // 計算隨機數的上下限 注意这里是1保证了每天能够足够执行十次
        const min = Math.max(1, maxSearchCount - range);
        const max = maxSearchCount + range;
        // 生成隨機整數，包括min和max
        // Math.random() 生成 [0,1) 的浮點數
        // Math.floor(Math.random() * (max - min + 1)) 生成 [0, max-min] 的整數
        // 最後加上 min，得到 [min, max] 的整數
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    //阅读赚积分
    public async handleReadToEarn(accessToken: string, userData: AppUserDataResponse, account: AccountInfo) {
        const msnreadearns = userData.promotions.filter((x) => x.attributes.offerid === 'ENUS_readarticle3_30points');
        if (msnreadearns.length <= 0) {
            return;
        }
        const msnreadearn = msnreadearns[0];
        const msnreadearnEarnablePoints = (parseInt(msnreadearn.attributes.pointmax) - parseInt(msnreadearn.attributes.pointprogress));
        if (msnreadearnEarnablePoints <= 0) {
            this.logger.info(account.email, `msnreadearn今天已完成`);
            return;
        }
        this.logger.info(account.email, `msnreadearn尚有${msnreadearnEarnablePoints}分可领取`);

        let limitCount = this.config.search.defaultMaxReadPerRequest;
        const readTotalCount = parseInt(msnreadearn.attributes.pointprogress) / 3;
        const score = userData.balance;
        const searchLimit = Config.getInstance().getLimitByScore(score);

        // 首先检查账号中是否有自定义的阅读限制
        if (account && account.maxReadPerRequest != undefined && account.maxReadPerRequest >= 0) {
            // 如果账号中有自定义的阅读限制且大于0，则使用账号中的限制
            limitCount = account.maxReadPerRequest;
        } else if (searchLimit && searchLimit.maxReadPerRequest != undefined && searchLimit.maxReadPerRequest >= 0) {
            limitCount = searchLimit.maxReadPerRequest;
        }
        if (account && account.maxDailyReadLimit != undefined && account.maxDailyReadLimit >= 0) {
            let remainReadCount = account.maxDailyReadLimit - readTotalCount;
            remainReadCount = Math.max(0, remainReadCount);
            limitCount = Math.min(remainReadCount, limitCount);
        } else if (searchLimit && searchLimit.maxDailyReadLimit != undefined && searchLimit.maxDailyReadLimit >= 0) {
            let remainReadCount = searchLimit.maxDailyReadLimit - readTotalCount;
            remainReadCount = Math.max(0, remainReadCount);
            limitCount = Math.min(remainReadCount, limitCount);
        }

        if (limitCount <= 0) {
            return;
        }
        const readCount = Math.min(msnreadearnEarnablePoints / 3, this.getRandomReadCount(limitCount));
        if (readCount <= 0) {
            return;
        }
        for (let i = 0; i < readCount; i++) {
            try {
                const claimResponse = await fetch('https://prod.rewardsplatform.microsoft.com/dapi/me/activities', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'X-Rewards-Country': this.config.reward.country,
                        'X-Rewards-Language': this.config.reward.language,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        id: crypto.randomBytes(64).toString('hex'),
                        amount: 1,
                        country: this.config.reward.country,
                        type: 101,
                        attributes: { offerid: 'ENUS_readarticle3_30points' }
                    })
                });
                if (!claimResponse.ok) {
                    throw new Error(`HTTP error! status: ${claimResponse.status}`);
                }
                const responseData = await claimResponse.json();
                const claimedPoint = parseInt(responseData.response.activity.p);
                //p>0表示领取的积分
                this.logger.info('READ-TO-EARN', claimedPoint > 0 ? `阅读获取${claimedPoint}点` : '今日没有可阅读的了')
                //等待5-10秒的阅读时间
                const delay = getRandomInRange(this.config.search.readTimeMin, this.config.search.readTimeMax) * 1000;
                await new Promise(resolve => setTimeout(resolve, delay));
            }
            catch (error) {
                this.logger.info('READ-TO-EARN', 'An error occurred:' + error)
            }
            //等待下次间隔时间
            const delay = getRandomInRange(this.config.search.nextSearchMin, this.config.search.nextSearchMax) * 1000;
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    private async getAuthorizationCode(page: Page, email: string): Promise<string> {

        const clientId: string = '0000000040170455';
        const redirectUrl: string = 'https://login.live.com/oauth20_desktop.srf';
        const scope = 'service::prod.rewardsplatform.microsoft.com::MBI_SSL';
        const authorizeUrl = new URL('https://login.live.com/oauth20_authorize.srf');

        authorizeUrl.searchParams.append('client_id', clientId);
        authorizeUrl.searchParams.append('scope', scope);
        authorizeUrl.searchParams.append('redirect_uri', redirectUrl);
        authorizeUrl.searchParams.append('response_type', 'code');
        authorizeUrl.searchParams.append('state', crypto.randomBytes(16).toString('hex'));
        authorizeUrl.searchParams.append('access_type', 'offline_access');
        authorizeUrl.searchParams.append('login_hint', email);
        let code: string = "";
        let attempts = 0;
        const maxAttempts = 10;
        const retryDelay = 1000;
        while (attempts < maxAttempts) {
            this.logger.info(email, `开始第${attempts + 1}次获取授权码`);
            await page.goto(authorizeUrl.href);
            //开始认证,注意这中间也是一个登录过程..登录信息可能会失效

            await page.waitForTimeout(5000);

            if (page.url().startsWith("https://login.live.com/oauth20_authorize.srf")) {
                throw new AuthorizationCodeError(`获取授权码失败: 删除过期cookie,下次登录后进行认证`);
            }

            for (let i = 0; i < 2; i++) {
                try {
                    await page.waitForURL('https://account.live.com/interrupt/**', { timeout: 3000 });

                    // 尝试查找"暂时跳过"按钮
                    const skipButtonExists = await page.isVisible('button[data-testid="secondaryButton"]', { timeout: 5000 });
                    if (skipButtonExists) {
                        this.logger.info(email, "找到新版'暂时跳过'按钮，正在点击...");
                        await page.click('button[data-testid="secondaryButton"]');
                    }

                    // 最后尝试你提到的另一个按钮
                    const otherButtonExists = await page.isVisible('div[data-testid="textButtonContainer"] > div:first-child > button[type="button"]', { timeout: 5000 });
                    if (otherButtonExists) {
                        this.logger.info(email, "找到旧版'暂时跳过'按钮，正在点击...");
                        await page.click('div[data-testid="textButtonContainer"] > div:first-child > button[type="button"]');
                    }
                    await page.waitForTimeout(1000 * 5);
                } catch (error) {
                    //暂时跳过.下一个.获取微软的通行密钥软件
                    this.logger.info(email, `无<使用人脸、指纹或 PIN 更快地登录>，继续执行: ${error}`);
                }
            }

            // 处理安全提示
            try {
                await page.waitForURL('https://login.live.com/oauth20_desktop.srf**', { timeout: 5000 });
                // 获取code参数
                const currentUrl = new URL(page.url());
                code = currentUrl.searchParams.get('code') || '';
                if (code) {
                    this.logger.info(email, `成功获取授权码${code}`);
                    return code;
                }
            } catch (error) {
                this.logger.info(email, `等待跳转超时: ${error}`);
            }
            attempts++;
            if (attempts < maxAttempts) {
                const delay = retryDelay * attempts; // 递增等待时间
                this.logger.info(email, `获取授权码失败 (${attempts}/${maxAttempts}),等待${delay / 1000}秒后重试`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }

        throw new Error(`获取授权码失败: 已重试${maxAttempts}次`);
    }

    async getMobileAccessToken(page: Page, email: string) {
        const clientId: string = '0000000040170455';
        const redirectUrl: string = 'https://login.live.com/oauth20_desktop.srf';
        const code = await this.getAuthorizationCode(page, email);
        try {
            await page.goto('https://rewards.bing.com');
        }
        catch (error) {
            this.logger.info(email, "getMobileAccessToken中加载rewards.bing.com页面超时,但仍尝试获取token");
        }
        try {
            let attempts = 0;
            const maxAttempts = 10;
            const retryDelay = 1500;
            while (attempts < maxAttempts) {
                try {
                    const body = new URLSearchParams()
                    body.append('grant_type', 'authorization_code')
                    body.append('client_id', clientId)
                    body.append('code', code)
                    body.append('redirect_uri', redirectUrl)
                    const tokenResponse = await fetch('https://login.microsoftonline.com/consumers/oauth2/v2.0/token', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        },
                        body: body.toString()
                    })

                    if (!tokenResponse.ok) {
                        throw new Error(`HTTP error! status: ${tokenResponse.status}`);
                    }

                    const tokenData: OAuth = await tokenResponse.json();
                    this.logger.info('Mobile', 'Successfully authorized')
                    return tokenData.access_token
                }
                catch (error) {
                    attempts++;
                    this.logger.info('Mobile', `获取token失败，第${attempts}次重试: ${error}`);
                    if (attempts >= maxAttempts) {
                        throw new Error(`Mobile Get Token Failed after ${maxAttempts} attempts`);
                    }
                    // 重试前等待
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
            }
            throw new Error('Mobile Get Token Failed: Max attempts reached');
        }
        catch (error) {
            throw new Error(`Mobile Get Token Failed: ${error}`);
        }
    }

    /**
    * Get total earnable points with mobile app
    * @returns {number} Total earnable points
   */

    async getAppUserData(accessToken: string): Promise<AppUserDataResponse> {
        const maxRetries = 10;
        const retryDelay = 1500;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const response = await fetch('https://prod.rewardsplatform.microsoft.com/dapi/me?channel=SAAndroid&options=613', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        "x-rewards-partnerid": "startapp",
                        "x-rewards-appid": "SAAndroid/30.4.430116016",
                        'X-Rewards-Country': this.config.reward.country,
                        'X-Rewards-Language': this.config.reward.language,
                        "x-rewards-flights": "rwgobig"
                    }
                });

                // Check if response is ok (status in the range 200-299)
                if (!response.ok) {
                    throw new Error(`Server responded with status: ${response.status}`);
                }

                const userDataResponse: AppUserData = await response.json();
                const userData = userDataResponse.response;

                return userData;
            } catch (error) {
                // On last attempt, throw the error
                if (attempt === maxRetries) {
                    throw new Error(`Failed to get app earnable points after ${maxRetries} attempts: ${error}`);
                }

                // Log the retry attempt
                this.logger.info("获取移动端用户数据失败", `Attempt ${attempt} failed: ${error}. Retrying in ${retryDelay}ms...`);

                // Wait before next retry
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
        }

        // This should never be reached due to the throw in the catch block
        throw new Error('Unexpected execution path');
    }



}
